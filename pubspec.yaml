name: novel_app
description: 岱宗文脉 - AI小说生成器
publish_to: 'none'
version: 4.3.01

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  get: ^4.6.6
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  uuid: ^4.3.3
  path_provider: ^2.1.2
  share_plus: ^7.2.1
  cupertino_icons: ^1.0.2
  get_storage: ^2.1.1
  http: ^1.1.0
  permission_handler: ^11.3.0
  device_info_plus: ^9.1.2
  intl: ^0.18.1
  url_launcher: ^6.2.5
  shared_preferences: ^2.2.2
  crypto: ^3.0.3
  just_audio: ^0.9.34
  just_audio_windows: ^0.2.0
  msix: ^3.16.7
  pdf: ^3.10.8
  path: ^1.8.3
  html: ^0.15.0
  # file_picker: ^6.1.1 # 由于V1 embedding移除问题，暂时禁用
  file_selector: ^1.0.2 # 替代file_picker的插件
  flutter_svg: ^2.0.10+1
  gbk_codec: ^0.4.0
  langchain: ^0.3.0
  langchain_openai: ^0.3.0
  google_generative_ai: ^0.4.7
  flutter_markdown: ^0.7.1
  package_info_plus: ^4.0.0
  google_fonts: ^6.1.0  # 添加Google Fonts支持，减少本地字体文件大小
  # wakelock: ^0.4.0 # 暂时禁用，构建时有问题

  # 向量数据库和嵌入相关
  vector_math: ^2.1.4
  collection: ^1.17.2
  sqflite: ^2.3.0

  # JSON序列化
  json_annotation: ^4.8.1

  # 网络请求库
  dio: ^5.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.8
  hive_generator: ^2.0.1
  json_serializable: ^6.7.1
  analyzer: ^6.7.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/version.json

  # 注释掉本地字体文件，改用Google Fonts在线字体
  # fonts:
  #   - family: NotoSerifSC
  #     fonts:
  #       - asset: assets/fonts/NotoSerifSC-Regular.otf
  #         weight: 400

msix_config:
  display_name: "岱宗文脉"
  publisher_display_name: "岱宗文脉"
  identity_name: "com.daizhong.novelapp"
  publisher: "CN=岱宗文脉"
  msix_version: "4.3.0.0"
  capabilities: "internetClient,musicLibrary,documentsLibrary"
  execution_alias: "novelapp"
  languages: "zh-cn"
  store: false
  architecture: "x64"
  sign_msix: false
  build_windows: true